{"name": "bod<PERSON>i", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 3000", "build": "vite build", "preview": "vite preview --port 5000", "sso-proxy": "node sso-proxy-server.js", "dev:full": "concurrently \"npm run sso-proxy\" \"npm run dev\""}, "dependencies": {"axios": "^1.10.0", "buffer": "^6.0.3", "cors": "^2.8.5", "express": "^4.21.2", "file-type": "^21.0.0", "highlight.js": "^11.11.1", "jszip": "^3.10.1", "mammoth": "^1.9.1", "marked": "^16.0.0", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "redis": "^5.5.6", "vue": "^3.5.17", "word-extractor": "^1.0.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.2", "vite": "^4.5.0", "vite-plugin-vue-devtools": "^7.7.7"}}