<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档解析API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>文档解析API测试</h1>

    <div class="test-section">
        <h2>DOC文件解析测试</h2>
        <p>测试DOC解析API是否可访问</p>
        <button onclick="testDocAPIConnection()">测试DOC API连接</button>
        <div id="docConnectionResult" class="result"></div>

        <p>选择一个DOC文件进行解析测试</p>
        <input type="file" id="docFile" accept=".doc" />
        <button onclick="testDocFileUpload()">上传并解析DOC</button>
        <div id="docUploadResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>PDF文件解析测试</h2>
        <p>测试PDF解析API是否可访问</p>
        <button onclick="testPdfAPIConnection()">测试PDF API连接</button>
        <div id="pdfConnectionResult" class="result"></div>

        <p>选择一个PDF文件进行解析测试</p>
        <input type="file" id="pdfFile" accept=".pdf" />
        <button onclick="testPdfFileUpload()">上传并解析PDF</button>
        <div id="pdfUploadResult" class="result"></div>
    </div>

    <script>
        // DOC API测试函数
        async function testDocAPIConnection() {
            const resultDiv = document.getElementById('docConnectionResult');
            resultDiv.textContent = '正在测试DOC API连接...';
            resultDiv.className = 'result';

            try {
                // 测试API是否可访问（发送一个空请求）
                const response = await fetch('/api/doc-parse', {
                    method: 'POST',
                    body: new FormData() // 空的FormData
                });

                if (response.ok) {
                    resultDiv.textContent = 'DOC API连接成功！状态码: ' + response.status;
                    resultDiv.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultDiv.textContent = `DOC API连接失败！状态码: ${response.status}\n错误信息: ${errorText}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `网络错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testDocFileUpload() {
            const fileInput = document.getElementById('docFile');
            const resultDiv = document.getElementById('docUploadResult');
            
            if (!fileInput.files[0]) {
                resultDiv.textContent = '请先选择一个DOC文件';
                resultDiv.className = 'result error';
                return;
            }

            const file = fileInput.files[0];
            
            // 检查文件类型
            if (!file.name.toLowerCase().endsWith('.doc')) {
                resultDiv.textContent = '请选择.doc格式的文件';
                resultDiv.className = 'result error';
                return;
            }

            resultDiv.textContent = `正在上传并解析文件: ${file.name} (${formatFileSize(file.size)})...`;
            resultDiv.className = 'result';

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/doc-parse', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const content = await response.text();
                    resultDiv.textContent = `解析成功！\n\n文件名: ${file.name}\n文件大小: ${formatFileSize(file.size)}\n解析内容长度: ${content.length} 字符\n\n解析内容:\n${content}`;
                    resultDiv.className = 'result success';
                } else {
                    const errorText = await response.text();
                    resultDiv.textContent = `解析失败！\n状态码: ${response.status}\n错误信息: ${errorText}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `上传失败: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
