<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引用文件链接功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-title {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-output {
            background: #1f2937;
            color: #d1d5db;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .api-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .api-info h4 {
            margin: 0 0 10px 0;
            color: #92400e;
        }
        
        .api-info code {
            background: #fbbf24;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 13px;
        }
        
        .mock-citation {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .file-link {
            color: #3b82f6;
            text-decoration: none;
            cursor: pointer;
            font-weight: 600;
        }
        
        .file-link:hover {
            color: #2563eb;
            text-decoration: underline;
        }
        
        .loading-indicator {
            color: #6b7280;
            font-size: 12px;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <h1>引用文件链接功能测试</h1>
    
    <div class="api-info">
        <h4>API接口信息</h4>
        <p><strong>请求URL:</strong> <code>http://192.168.1.225:4001/getFileUrlByCollectionId</code></p>
        <p><strong>请求方式:</strong> GET</p>
        <p><strong>请求参数:</strong></p>
        <ul>
            <li><code>collectionId</code> - 集合的唯一标识（示例：685ba2186130313d1d0809c5）</li>
            <li><code>username</code> - 用户姓名（示例：苏二川）</li>
            <li><code>jobnumber</code> - 用户工号（示例：012652）</li>
        </ul>
        <p><strong>返回格式:</strong> 字符串（文件URL）</p>
    </div>
    
    <div class="test-section">
        <div class="test-title">1. 数据结构测试</div>
        <p>测试引用数据是否包含必要的字段，特别是 collectionId 字段。</p>
        <button class="test-button" onclick="testDataStructure()">测试数据结构</button>
        <div id="dataStructureOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. API调用测试</div>
        <p>模拟调用后台接口获取文件URL。</p>
        <button class="test-button" onclick="testApiCall()">测试API调用</button>
        <div id="apiCallOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 用户交互测试</div>
        <p>模拟用户点击文件名获取链接的交互过程。</p>
        <div class="mock-citation">
            <h4>模拟引用文件</h4>
            <span id="mockFileName" class="file-link" onclick="handleMockFileClick()">
                测试文档.pdf
                <span id="mockLoadingIndicator" class="loading-indicator" style="display: none;">获取链接中...</span>
            </span>
        </div>
        <div id="interactionOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">4. 完整流程测试</div>
        <p>运行完整的测试流程，验证所有功能。</p>
        <button class="test-button" onclick="runFullTest()">运行完整测试</button>
        <div id="fullTestOutput" class="test-output" style="display: none;"></div>
    </div>

    <script type="module">
        // 模拟数据
        const mockCitationData = {
            fileName: "测试文档.pdf",
            sourceId: "source123",
            quotes: [
                {
                    id: "quote1",
                    question: "这是一个测试问题？",
                    answer: "这是一个测试答案。",
                    chunkIndex: 0,
                    tokens: 50,
                    score: 0.95,
                    collectionId: "685ba2186130313d1d0809c5"
                }
            ]
        };

        const mockUserData = {
            username: "苏二川",
            employeeId: "012652"
        };

        // 测试函数
        window.testDataStructure = function() {
            const output = document.getElementById('dataStructureOutput');
            output.style.display = 'block';
            
            let result = '=== 数据结构测试 ===\n';
            result += `文件名: ${mockCitationData.fileName}\n`;
            result += `引用片段数量: ${mockCitationData.quotes.length}\n\n`;
            
            mockCitationData.quotes.forEach((quote, index) => {
                result += `引用片段 ${index + 1}:\n`;
                result += `  - ID: ${quote.id}\n`;
                result += `  - 问题: ${quote.question}\n`;
                result += `  - CollectionId: ${quote.collectionId}\n`;
                result += `  - 是否有CollectionId: ${!!quote.collectionId}\n\n`;
            });
            
            output.textContent = result;
        };

        window.testApiCall = function() {
            const output = document.getElementById('apiCallOutput');
            output.style.display = 'block';
            
            const collectionId = mockCitationData.quotes[0].collectionId;
            const params = new URLSearchParams({
                collectionId: collectionId,
                username: mockUserData.username,
                jobnumber: mockUserData.employeeId
            });

            const testUrl = `http://192.168.1.225:4001/getFileUrlByCollectionId?${params}`;
            
            let result = '=== API调用测试 ===\n';
            result += `请求URL: ${testUrl}\n\n`;
            result += '请求参数:\n';
            result += `  - collectionId: ${collectionId}\n`;
            result += `  - username: ${mockUserData.username}\n`;
            result += `  - jobnumber: ${mockUserData.employeeId}\n\n`;
            result += '注意: 这是模拟测试，实际需要后台服务支持\n';
            result += '预期返回: 文件URL字符串\n';
            
            output.textContent = result;
        };

        window.handleMockFileClick = async function() {
            const output = document.getElementById('interactionOutput');
            const loadingIndicator = document.getElementById('mockLoadingIndicator');
            const fileName = document.getElementById('mockFileName');
            
            output.style.display = 'block';
            loadingIndicator.style.display = 'inline';
            
            let result = '=== 用户交互测试 ===\n';
            result += '1. 用户点击文件名\n';
            result += '2. 显示加载指示器\n';
            result += '3. 查找包含collectionId的引用片段\n';
            
            const quoteWithCollectionId = mockCitationData.quotes.find(quote => quote.collectionId);
            if (quoteWithCollectionId) {
                result += `4. 找到collectionId: ${quoteWithCollectionId.collectionId}\n`;
                result += '5. 模拟API调用...\n';
                
                // 模拟API调用延迟
                setTimeout(() => {
                    loadingIndicator.style.display = 'none';
                    result += '6. API调用成功，获取到文件URL\n';
                    result += '7. 在新窗口打开文件链接\n';
                    result += '模拟文件URL: https://example.com/files/test-document.pdf\n';
                    output.textContent = result;
                }, 2000);
            } else {
                loadingIndicator.style.display = 'none';
                result += '4. 未找到collectionId，无法获取文件链接\n';
            }
            
            output.textContent = result;
        };

        window.runFullTest = function() {
            const output = document.getElementById('fullTestOutput');
            output.style.display = 'block';
            
            let result = '=== 完整流程测试 ===\n\n';
            
            // 1. 数据结构验证
            result += '1. 验证数据结构...\n';
            result += `   ✓ 文件名: ${mockCitationData.fileName}\n`;
            result += `   ✓ 引用片段数量: ${mockCitationData.quotes.length}\n`;
            result += `   ✓ CollectionId存在: ${!!mockCitationData.quotes[0].collectionId}\n\n`;
            
            // 2. 用户信息验证
            result += '2. 验证用户信息...\n';
            result += `   ✓ 用户名: ${mockUserData.username}\n`;
            result += `   ✓ 工号: ${mockUserData.employeeId}\n\n`;
            
            // 3. API参数构建
            result += '3. 构建API请求参数...\n';
            const collectionId = mockCitationData.quotes[0].collectionId;
            const params = new URLSearchParams({
                collectionId: collectionId,
                username: mockUserData.username,
                jobnumber: mockUserData.employeeId
            });
            result += `   ✓ CollectionId: ${collectionId}\n`;
            result += `   ✓ 请求URL: http://192.168.1.225:4001/getFileUrlByCollectionId?${params}\n\n`;
            
            // 4. 功能流程
            result += '4. 功能流程验证...\n';
            result += '   ✓ 用户点击文件名触发事件\n';
            result += '   ✓ 显示加载状态\n';
            result += '   ✓ 查找collectionId成功\n';
            result += '   ✓ 构建API请求\n';
            result += '   ✓ 获取文件URL\n';
            result += '   ✓ 在新窗口打开链接\n\n';
            
            result += '测试完成！所有功能验证通过。\n';
            result += '\n注意事项:\n';
            result += '- 实际使用需要确保后台API服务正常运行\n';
            result += '- 需要用户已登录并有有效的用户信息\n';
            result += '- collectionId必须存在于引用数据中\n';
            
            output.textContent = result;
        };
    </script>
</body>
</html>
