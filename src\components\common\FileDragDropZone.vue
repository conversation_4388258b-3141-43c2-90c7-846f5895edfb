<template>
  <div class="file-drag-drop-zone">
    <!-- 拖拽区域 -->
    <div 
      class="drop-zone"
      :class="{ 
        'drag-over': isDragOver, 
        'has-files': parsedFiles.length > 0,
        'disabled': disabled 
      }"
      @dragenter.prevent="handleDragEnter"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      @click="triggerFileSelect"
    >
      <!-- 空状态 -->
      <div v-if="parsedFiles.length === 0" class="empty-state">
        <div class="drop-icon">📁</div>
        <div class="drop-text">
          <p class="primary-text">拖拽文档文件到此处</p>
          <p class="secondary-text">支持 DOCX、PDF、DOC、TXT 格式</p>
          <p class="hint-text">或点击选择文件</p>
        </div>
      </div>

      <!-- 文件列表 -->
      <div v-else class="files-list">
        <div class="files-header">
          <span class="files-count">已选择 {{ parsedFiles.length }} 个文件</span>
          <button 
            class="clear-all-btn" 
            @click.stop="clearAllFiles"
            title="清空所有文件"
          >
            ✕
          </button>
        </div>
        
        <div class="files-container">
          <div 
            v-for="(file, index) in parsedFiles" 
            :key="index"
            class="file-item"
            :class="{ 'parsing': file.parsing, 'error': file.error }"
          >
            <div class="file-icon">
              {{ getFileIcon(file.fileName) }}
            </div>
            
            <div class="file-info">
              <div class="file-name" :title="file.fileName">
                {{ file.fileName }}
              </div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
                <span v-if="file.parsing" class="parsing-status">解析中...</span>
                <span v-else-if="file.error" class="error-status">{{ file.error }}</span>
                <span v-else-if="file.success" class="success-status">
                  已解析 ({{ file.content.length }} 字符)
                </span>
              </div>
            </div>

            <button 
              class="remove-file-btn"
              @click.stop="removeFile(index)"
              title="移除文件"
            >
              ✕
            </button>
          </div>
        </div>

        <!-- 添加更多文件按钮 -->
        <div class="add-more-section">
          <button 
            class="add-more-btn"
            @click.stop="triggerFileSelect"
            :disabled="disabled"
          >
            + 添加更多文件
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".docx,.pdf,.doc,.txt,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,application/msword,text/plain"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 解析进度提示 -->
    <div v-if="isProcessing" class="processing-overlay">
      <div class="processing-content">
        <div class="spinner"></div>
        <p>正在解析文档...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getDocumentParserService } from '../../services/documentParserService.js'

// 组件属性
const props = defineProps({
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 最大文件数量
  maxFiles: {
    type: Number,
    default: 5
  },
  // 最大文件大小（字节）
  maxFileSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  }
})

// 组件事件
const emit = defineEmits(['files-parsed', 'files-changed', 'error'])

// 响应式数据
const isDragOver = ref(false)
const parsedFiles = ref([])
const fileInput = ref(null)
const isProcessing = ref(false)

// 计算属性
const hasValidFiles = computed(() => {
  return parsedFiles.value.some(file => file.success && file.content.trim())
})

// 获取文档解析服务
const documentParser = getDocumentParserService()

/**
 * 处理拖拽进入
 */
const handleDragEnter = (event) => {
  if (props.disabled) return
  isDragOver.value = true
}

/**
 * 处理拖拽悬停
 */
const handleDragOver = (event) => {
  if (props.disabled) return
  isDragOver.value = true
}

/**
 * 处理拖拽离开
 */
const handleDragLeave = (event) => {
  // 检查是否真的离开了拖拽区域
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

/**
 * 处理文件拖拽放置
 */
const handleDrop = async (event) => {
  if (props.disabled) return
  
  isDragOver.value = false
  const files = Array.from(event.dataTransfer.files)
  
  if (files.length === 0) return
  
  await processFiles(files)
}

/**
 * 触发文件选择
 */
const triggerFileSelect = () => {
  if (props.disabled) return
  fileInput.value?.click()
}

/**
 * 处理文件选择
 */
const handleFileSelect = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return
  
  await processFiles(files)
  
  // 清空文件输入，允许重复选择同一文件
  event.target.value = ''
}

/**
 * 处理文件列表
 */
const processFiles = async (files) => {
  // 检查文件数量限制
  if (parsedFiles.value.length + files.length > props.maxFiles) {
    emit('error', `最多只能选择 ${props.maxFiles} 个文件`)
    return
  }

  // 过滤和验证文件
  const validFiles = []
  for (const file of files) {
    // 检查文件大小
    if (file.size > props.maxFileSize) {
      emit('error', `文件 "${file.name}" 超过大小限制 (${formatFileSize(props.maxFileSize)})`)
      continue
    }

    // 检查文件类型
    if (!documentParser.isSupportedFile(file)) {
      emit('error', `不支持的文件类型: "${file.name}"`)
      continue
    }

    // 检查是否已存在
    if (parsedFiles.value.some(f => f.fileName === file.name && f.fileSize === file.size)) {
      emit('error', `文件 "${file.name}" 已存在`)
      continue
    }

    validFiles.push(file)
  }

  if (validFiles.length === 0) return

  // 开始解析文件
  isProcessing.value = true

  try {
    for (const file of validFiles) {
      // 添加到列表（解析中状态）
      const fileItem = {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        content: '',
        success: false,
        error: null,
        parsing: true
      }
      
      parsedFiles.value.push(fileItem)
      
      // 异步解析文件
      try {
        const result = await documentParser.parseDocument(file)
        
        // 更新文件状态
        const index = parsedFiles.value.findIndex(f => 
          f.fileName === file.name && f.fileSize === file.size && f.parsing
        )
        
        if (index !== -1) {
          parsedFiles.value[index] = {
            ...result,
            parsing: false
          }
        }
      } catch (error) {
        // 更新错误状态
        const index = parsedFiles.value.findIndex(f => 
          f.fileName === file.name && f.fileSize === file.size && f.parsing
        )
        
        if (index !== -1) {
          parsedFiles.value[index] = {
            ...fileItem,
            parsing: false,
            error: error.message
          }
        }
      }
    }

    // 触发事件
    emitFilesChanged()
    
  } finally {
    isProcessing.value = false
  }
}

/**
 * 移除文件
 */
const removeFile = (index) => {
  parsedFiles.value.splice(index, 1)
  emitFilesChanged()
}

/**
 * 清空所有文件
 */
const clearAllFiles = () => {
  parsedFiles.value = []
  emitFilesChanged()
}

/**
 * 触发文件变化事件
 */
const emitFilesChanged = () => {
  const validFiles = parsedFiles.value.filter(file => file.success && file.content.trim())
  emit('files-changed', parsedFiles.value)
  emit('files-parsed', validFiles)
}

/**
 * 获取文件图标
 */
const getFileIcon = (fileName) => {
  const ext = fileName.toLowerCase().split('.').pop()
  const iconMap = {
    'docx': '📄',
    'doc': '📄',
    'pdf': '📕',
    'txt': '📝'
  }
  return iconMap[ext] || '📄'
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  clearAllFiles,
  getParsedFiles: () => parsedFiles.value.filter(file => file.success),
  hasValidFiles
})
</script>

<style scoped>
.file-drag-drop-zone {
  position: relative;
  width: 100%;
}

.drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f9fafb;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover:not(.disabled) {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #dbeafe;
  transform: scale(1.02);
}

.drop-zone.has-files {
  text-align: left;
  align-items: stretch;
  min-height: auto;
  padding: 16px;
}

.drop-zone.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.drop-icon {
  font-size: 16px;
  opacity: 0.6;
}

.drop-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.primary-text {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.secondary-text {
  font-size: 10px;
  color: #6b7280;
  margin: 0;
}

.hint-text {
  font-size: 9px;
  color: #9ca3af;
  margin: 0;
}

/* 文件列表样式 */
.files-list {
  width: 100%;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.files-count {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.clear-all-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s;
}

.clear-all-btn:hover {
  background-color: #fee2e2;
}

.files-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #ffffff;
  transition: all 0.2s;
}

.file-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-item.parsing {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.file-item.error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

.file-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.file-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
}

.file-size {
  color: #6b7280;
}

.parsing-status {
  color: #3b82f6;
  font-weight: 500;
}

.error-status {
  color: #ef4444;
  font-weight: 500;
}

.success-status {
  color: #10b981;
  font-weight: 500;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.remove-file-btn:hover {
  color: #ef4444;
  background-color: #fee2e2;
}

.add-more-section {
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.add-more-btn {
  background: none;
  border: 1px dashed #d1d5db;
  color: #6b7280;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  width: 100%;
}

.add-more-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
  background-color: #eff6ff;
}

.add-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 处理中遮罩 */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .drop-zone {
    border-color: #4b5563;
    background-color: #1f2937;
  }

  .drop-zone:hover:not(.disabled) {
    border-color: #60a5fa;
    background-color: #1e3a8a;
  }

  .drop-zone.drag-over {
    border-color: #60a5fa;
    background-color: #1e40af;
  }

  .file-item {
    background-color: #374151;
    border-color: #4b5563;
  }

  .file-name {
    color: #f3f4f6;
  }

  .files-count {
    color: #f3f4f6;
  }

  .primary-text {
    color: #f3f4f6;
  }

  .secondary-text {
    color: #d1d5db;
  }

  .hint-text {
    color: #9ca3af;
  }
}
</style>
