/**
 * 引用文件显示功能测试
 * 用于测试修复后的引用文件显示问题
 */

import { AIService } from '../services/aiService.js'
import { getAIConfig } from '../config/ai.js'

/**
 * 测试模拟模式下的引用生成
 */
async function testMockCitations() {
  console.log('=== 测试模拟模式下的引用生成 ===')
  
  // 测试知识中心模块
  const knowledgeConfig = getAIConfig('knowledge')
  const knowledgeService = new AIService(knowledgeConfig)
  
  console.log('知识中心配置:', {
    module: knowledgeConfig.module,
    enableMockMode: knowledgeConfig.enableMockMode
  })
  
  // 生成模拟引用
  const mockCitations = knowledgeService.generateMockCitations()
  console.log('知识中心模拟引用:', mockCitations)
  
  // 验证引用数据结构
  if (mockCitations && mockCitations.length > 0) {
    console.log('✓ 引用数据生成成功')
    console.log('引用文件数量:', mockCitations.length)
    
    mockCitations.forEach((citation, index) => {
      console.log(`文件 ${index + 1}:`)
      console.log('  - 文件名:', citation.fileName)
      console.log('  - 源ID:', citation.sourceId)
      console.log('  - 引用片段数量:', citation.quotes.length)
      
      citation.quotes.forEach((quote, quoteIndex) => {
        console.log(`  引用片段 ${quoteIndex + 1}:`)
        console.log('    - ID:', quote.id)
        console.log('    - 问题:', quote.question)
        console.log('    - 答案:', quote.answer)
        console.log('    - CollectionId:', quote.collectionId)
        console.log('    - 是否有CollectionId:', !!quote.collectionId)
      })
    })
  } else {
    console.log('✗ 引用数据生成失败')
  }
  
  return mockCitations
}

/**
 * 测试业务域的引用生成
 */
async function testBusinessCitations() {
  console.log('\n=== 测试业务域的引用生成 ===')
  
  const businessConfig = getAIConfig('business')
  const businessService = new AIService(businessConfig)
  
  const mockCitations = businessService.generateMockCitations()
  console.log('业务域模拟引用:', mockCitations)
  
  return mockCitations
}

/**
 * 测试职能域的引用生成
 */
async function testFunctionCitations() {
  console.log('\n=== 测试职能域的引用生成 ===')
  
  const functionConfig = getAIConfig('function')
  const functionService = new AIService(functionConfig)
  
  const mockCitations = functionService.generateMockCitations()
  console.log('职能域模拟引用:', mockCitations)
  
  return mockCitations
}

/**
 * 测试流式回复中的引用数据
 */
async function testStreamResponseCitations() {
  console.log('\n=== 测试流式回复中的引用数据 ===')
  
  const knowledgeConfig = getAIConfig('knowledge')
  const knowledgeService = new AIService(knowledgeConfig)
  
  // 模拟消息
  const mockMessages = [
    {
      type: 'user',
      content: '请介绍一下系统功能',
      timestamp: new Date()
    }
  ]
  
  let streamingContent = ''
  let reasoningContent = ''
  let finalResult = null
  
  try {
    // 调用流式回复方法
    finalResult = await knowledgeService.sendMessageStream(
      mockMessages,
      (chunk, fullContent) => {
        // 流式内容回调
        streamingContent = fullContent
        console.log('流式内容更新，长度:', fullContent.length)
      },
      (reasoningChunk, fullReasoningContent, isComplete) => {
        // 推理过程回调
        reasoningContent = fullReasoningContent
        if (isComplete) {
          console.log('推理过程完成，长度:', fullReasoningContent.length)
        }
      }
    )
    
    console.log('流式回复完成')
    console.log('最终结果类型:', typeof finalResult)
    console.log('最终结果结构:', Object.keys(finalResult || {}))
    
    if (finalResult && finalResult.citations) {
      console.log('✓ 引用数据存在')
      console.log('引用文件数量:', finalResult.citations.length)
      console.log('引用数据:', finalResult.citations)
    } else {
      console.log('✗ 引用数据不存在')
    }
    
  } catch (error) {
    console.error('流式回复测试失败:', error)
  }
  
  return finalResult
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始运行引用文件显示功能测试...\n')
  
  try {
    // 测试模拟引用生成
    const knowledgeCitations = await testMockCitations()
    const businessCitations = await testBusinessCitations()
    const functionCitations = await testFunctionCitations()
    
    // 测试流式回复
    const streamResult = await testStreamResponseCitations()
    
    console.log('\n=== 测试总结 ===')
    console.log('知识中心引用文件数量:', knowledgeCitations?.length || 0)
    console.log('业务域引用文件数量:', businessCitations?.length || 0)
    console.log('职能域引用文件数量:', functionCitations?.length || 0)
    console.log('流式回复引用文件数量:', streamResult?.citations?.length || 0)
    
    const totalCitations = (knowledgeCitations?.length || 0) + 
                          (businessCitations?.length || 0) + 
                          (functionCitations?.length || 0)
    
    if (totalCitations > 0 && streamResult?.citations?.length > 0) {
      console.log('✓ 所有测试通过，引用文件显示功能正常')
    } else {
      console.log('✗ 部分测试失败，需要进一步检查')
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error)
  }
  
  console.log('\n测试完成！')
}

// 导出测试函数
export {
  testMockCitations,
  testBusinessCitations,
  testFunctionCitations,
  testStreamResponseCitations,
  runAllTests
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
  window.citationDisplayTest = {
    testMockCitations,
    testBusinessCitations,
    testFunctionCitations,
    testStreamResponseCitations,
    runAllTests
  }
  
  console.log('引用文件显示测试已加载，可以通过 window.citationDisplayTest.runAllTests() 运行测试')
}
