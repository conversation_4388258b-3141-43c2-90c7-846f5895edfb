/**
 * 文档解析服务
 * 支持解析 DOCX、PDF、DOC 文件并提取文本内容
 */

import mammoth from 'mammoth'
import JSZ<PERSON> from 'jszip'

/**
 * 文档解析服务类
 */
class DocumentParserService {
  constructor() {
    this.supportedTypes = [
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/pdf', // .pdf
      'application/msword', // .doc
      'text/plain' // .txt
    ]
  }

  /**
   * 检查文件类型是否支持
   * @param {File} file - 文件对象
   * @returns {boolean} 是否支持
   */
  isSupportedFile(file) {
    if (!file) return false
    
    // 检查MIME类型
    if (this.supportedTypes.includes(file.type)) {
      return true
    }
    
    // 检查文件扩展名（作为备用方案）
    const fileName = file.name.toLowerCase()
    const supportedExtensions = ['.docx', '.pdf', '.doc', '.txt']
    return supportedExtensions.some(ext => fileName.endsWith(ext))
  }

  /**
   * 解析文档文件
   * @param {File} file - 文件对象
   * @returns {Promise<Object>} 解析结果
   */
  async parseDocument(file) {
    if (!this.isSupportedFile(file)) {
      throw new Error(`不支持的文件类型: ${file.type || '未知'}`)
    }

    try {
      console.log(`开始解析文档: ${file.name} (${file.type})`)
      
      let result = {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        content: '',
        success: false,
        error: null
      }

      // 根据文件类型选择解析方法
      if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || 
          file.name.toLowerCase().endsWith('.docx')) {
        result = await this.parseDocx(file, result)
      } else if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
        result = await this.parsePdf(file, result)
      } else if (file.type === 'application/msword' || file.name.toLowerCase().endsWith('.doc')) {
        result = await this.parseDoc(file, result)
      } else if (file.type === 'text/plain' || file.name.toLowerCase().endsWith('.txt')) {
        result = await this.parseTxt(file, result)
      }

      console.log(`文档解析完成: ${file.name}, 内容长度: ${result.content.length}`)
      return result

    } catch (error) {
      console.error(`文档解析失败: ${file.name}`, error)
      return {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        content: '',
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 解析DOCX文件
   * @param {File} file - 文件对象
   * @param {Object} result - 结果对象
   * @returns {Promise<Object>} 解析结果
   */
  async parseDocx(file, result) {
    try {
      const arrayBuffer = await file.arrayBuffer()
      const mammothResult = await mammoth.extractRawText({ arrayBuffer })
      
      result.content = mammothResult.value.trim()
      result.success = true
      
      // 记录警告信息（如果有）
      if (mammothResult.messages && mammothResult.messages.length > 0) {
        console.warn('DOCX解析警告:', mammothResult.messages)
      }
      
      return result
    } catch (error) {
      result.error = `DOCX解析失败: ${error.message}`
      return result
    }
  }

  /**
   * 解析PDF文件
   * @param {File} file - 文件对象
   * @param {Object} result - 结果对象
   * @returns {Promise<Object>} 解析结果
   */
  async parsePdf(file, result) {
    try {
      console.log(`开始调用API解析PDF文件: ${file.name}`)

      // 调用第三方API解析PDF文件
      const apiResult = await this.callPdfParseAPI(file)

      if (apiResult.success) {
        result.content = apiResult.content.trim()
        result.success = true
        console.log(`PDF文件API解析成功: ${file.name}, 内容长度: ${result.content.length}`)
        return result
      } else {
        // API调用失败，返回错误信息
        result.content = `[PDF文件: ${file.name}]\n\nAPI解析失败: ${apiResult.error}\n\n建议：\n1. 检查网络连接\n2. 确认PDF文件格式正确\n3. 或者复制文件内容到文本框中\n\n文件大小: ${this.formatFileSize(file.size)}`
        result.success = true // 标记为成功，因为我们提供了有用的信息
        result.error = apiResult.error
        return result
      }
    } catch (error) {
      console.error('PDF文件解析失败:', error)

      // 如果API调用出现异常，返回友好的错误信息
      result.content = `[PDF文件: ${file.name}]\n\n解析服务暂时不可用: ${error.message}\n\n建议：\n1. 稍后重试\n2. 确认PDF文件格式正确\n3. 或者复制文件内容到文本框中\n\n文件大小: ${this.formatFileSize(file.size)}`
      result.success = true // 标记为成功，因为我们提供了有用的信息
      result.error = error.message

      return result
    }
  }

  /**
   * 调用第三方API解析PDF文件
   * @param {File} file - PDF文件对象
   * @returns {Promise<Object>} API调用结果
   */
  async callPdfParseAPI(file) {
    // 使用Vite代理路径，避免CORS问题
    const API_URL = '/api/pdf-parse'
    const MAX_RETRY = 2 // 最大重试次数

    for (let attempt = 1; attempt <= MAX_RETRY; attempt++) {
      try {
        console.log(`第${attempt}次尝试调用PDF解析API...`)

        // 创建FormData对象
        const formData = new FormData()
        formData.append('file', file)

        // 发送请求
        const response = await fetch(API_URL, {
          method: 'POST',
          body: formData,
          // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
        })

        if (response.ok) {
          // 成功响应，获取文本内容
          const content = await response.text()

          if (content && content.trim().length > 0) {
            return {
              success: true,
              content: content,
              attempt: attempt
            }
          } else {
            return {
              success: false,
              error: 'API返回空内容',
              attempt: attempt
            }
          }
        } else {
          // HTTP错误状态
          const errorText = await response.text().catch(() => '无法获取错误详情')
          const error = `HTTP ${response.status}: ${response.statusText}. ${errorText}`

          if (attempt === MAX_RETRY) {
            return {
              success: false,
              error: error,
              attempt: attempt
            }
          }

          console.warn(`第${attempt}次API调用失败: ${error}，准备重试...`)
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      } catch (error) {
        console.error(`第${attempt}次API调用异常:`, error)

        if (attempt === MAX_RETRY) {
          return {
            success: false,
            error: `网络错误: ${error.message}`,
            attempt: attempt
          }
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }

    // 理论上不会到达这里
    return {
      success: false,
      error: '未知错误',
      attempt: MAX_RETRY
    }
  }

  /**
   * 解析DOC文件
   * @param {File} file - 文件对象
   * @param {Object} result - 结果对象
   * @returns {Promise<Object>} 解析结果
   */
  async parseDoc(file, result) {
    try {
      console.log(`开始调用API解析DOC文件: ${file.name}`)

      // 调用第三方API解析DOC文件
      const apiResult = await this.callDocParseAPI(file)

      if (apiResult.success) {
        result.content = apiResult.content.trim()
        result.success = true
        console.log(`DOC文件API解析成功: ${file.name}, 内容长度: ${result.content.length}`)
        return result
      } else {
        // API调用失败，返回错误信息
        result.content = `[DOC文件: ${file.name}]\n\nAPI解析失败: ${apiResult.error}\n\n建议：\n1. 检查网络连接\n2. 将文件转换为DOCX格式\n3. 或者复制文件内容到文本框中\n\n文件大小: ${this.formatFileSize(file.size)}`
        result.success = true // 标记为成功，因为我们提供了有用的信息
        result.error = apiResult.error
        return result
      }
    } catch (error) {
      console.error('DOC文件解析失败:', error)

      // 如果API调用出现异常，返回友好的错误信息
      result.content = `[DOC文件: ${file.name}]\n\n解析服务暂时不可用: ${error.message}\n\n建议：\n1. 稍后重试\n2. 将文件转换为DOCX格式\n3. 或者复制文件内容到文本框中\n\n文件大小: ${this.formatFileSize(file.size)}`
      result.success = true // 标记为成功，因为我们提供了有用的信息
      result.error = error.message

      return result
    }
  }

  /**
   * 调用第三方API解析DOC文件
   * @param {File} file - DOC文件对象
   * @returns {Promise<Object>} API调用结果
   */
  async callDocParseAPI(file) {
    // 使用Vite代理路径，避免CORS问题
    const API_URL = '/api/doc-parse'
    const MAX_RETRY = 2 // 最大重试次数

    for (let attempt = 1; attempt <= MAX_RETRY; attempt++) {
      try {
        console.log(`第${attempt}次尝试调用DOC解析API...`)

        // 创建FormData对象
        const formData = new FormData()
        formData.append('file', file)

        // 发送请求
        const response = await fetch(API_URL, {
          method: 'POST',
          body: formData,
          // 不设置Content-Type，让浏览器自动设置multipart/form-data边界
        })

        if (response.ok) {
          // 成功响应，获取文本内容
          const content = await response.text()

          if (content && content.trim().length > 0) {
            return {
              success: true,
              content: content,
              attempt: attempt
            }
          } else {
            return {
              success: false,
              error: 'API返回空内容',
              attempt: attempt
            }
          }
        } else {
          // HTTP错误状态
          const errorText = await response.text().catch(() => '无法获取错误详情')
          const error = `HTTP ${response.status}: ${response.statusText}. ${errorText}`

          if (attempt === MAX_RETRY) {
            return {
              success: false,
              error: error,
              attempt: attempt
            }
          }

          console.warn(`第${attempt}次API调用失败: ${error}，准备重试...`)
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        }
      } catch (error) {
        console.error(`第${attempt}次API调用异常:`, error)

        if (attempt === MAX_RETRY) {
          return {
            success: false,
            error: `网络错误: ${error.message}`,
            attempt: attempt
          }
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }

    // 理论上不会到达这里
    return {
      success: false,
      error: '未知错误',
      attempt: MAX_RETRY
    }
  }

  /**
   * 解析TXT文件
   * @param {File} file - 文件对象
   * @param {Object} result - 结果对象
   * @returns {Promise<Object>} 解析结果
   */
  async parseTxt(file, result) {
    try {
      const text = await file.text()
      result.content = text.trim()
      result.success = true
      return result
    } catch (error) {
      result.error = `TXT解析失败: ${error.message}`
      return result
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 批量解析文档
   * @param {FileList|Array} files - 文件列表
   * @returns {Promise<Array>} 解析结果数组
   */
  async parseMultipleDocuments(files) {
    const results = []
    
    for (const file of files) {
      try {
        const result = await this.parseDocument(file)
        results.push(result)
      } catch (error) {
        results.push({
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          content: '',
          success: false,
          error: error.message
        })
      }
    }
    
    return results
  }
}

// 创建单例实例
const documentParserService = new DocumentParserService()

/**
 * 获取文档解析服务实例
 * @returns {DocumentParserService} 文档解析服务实例
 */
export const getDocumentParserService = () => {
  return documentParserService
}

// 默认导出
export default documentParserService
