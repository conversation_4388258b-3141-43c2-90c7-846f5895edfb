<template>
  <div class="image-drag-drop-zone">
    <!-- 拖拽区域 -->
    <div 
      class="drop-zone"
      :class="{ 
        'drag-over': isDragOver, 
        'has-images': uploadedImages.length > 0,
        'disabled': disabled 
      }"
      @dragenter.prevent="handleDragEnter"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      @click="triggerFileSelect"
    >
      <!-- 空状态 -->
      <div v-if="uploadedImages.length === 0" class="empty-state">
        <div class="drop-icon">🖼️</div>
        <div class="drop-text">
          <p class="primary-text">拖拽图片文件到此处</p>
          <p class="secondary-text">支持 PNG、JPG 格式</p>
          <p class="hint-text">或点击选择图片</p>
        </div>
      </div>

      <!-- 图片列表 -->
      <div v-else class="images-list">
        <div class="images-header">
          <span class="images-count">已选择 {{ uploadedImages.length }} 张图片</span>
          <button 
            class="clear-all-btn" 
            @click.stop="clearAllImages"
            title="清空所有图片"
          >
            ✕
          </button>
        </div>
        
        <div class="images-container">
          <div 
            v-for="(image, index) in uploadedImages" 
            :key="index"
            class="image-item"
            :class="{ 'processing': image.processing, 'error': image.error }"
          >
            <div class="image-preview">
              <img 
                v-if="image.preview" 
                :src="image.preview" 
                :alt="image.fileName"
                class="preview-img"
              />
              <div v-else class="preview-placeholder">
                🖼️
              </div>
            </div>
            
            <div class="image-info">
              <div class="image-name" :title="image.fileName">
                {{ image.fileName }}
              </div>
              <div class="image-meta">
                <span class="image-size">{{ formatFileSize(image.fileSize) }}</span>
                <span v-if="image.processing" class="processing-status">处理中...</span>
                <span v-else-if="image.error" class="error-status">{{ image.error }}</span>
                <span v-else-if="image.base64" class="success-status">
                  已转换为Base64
                </span>
              </div>
            </div>

            <button 
              class="remove-image-btn"
              @click.stop="removeImage(index)"
              title="移除图片"
            >
              ✕
            </button>
          </div>
        </div>

        <!-- 添加更多图片按钮 -->
        <div class="add-more-section">
          <button 
            class="add-more-btn"
            @click.stop="triggerFileSelect"
            :disabled="disabled"
          >
            + 添加更多图片
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept="image/png,image/jpeg,image/jpg,.png,.jpg,.jpeg"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 处理进度提示 -->
    <div v-if="isProcessing" class="processing-overlay">
      <div class="processing-content">
        <div class="spinner"></div>
        <p>正在处理图片...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 组件属性
const props = defineProps({
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 最大图片数量
  maxImages: {
    type: Number,
    default: 5
  },
  // 最大文件大小（字节）
  maxFileSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  }
})

// 组件事件
const emit = defineEmits(['images-processed', 'images-changed', 'error'])

// 响应式数据
const isDragOver = ref(false)
const uploadedImages = ref([])
const fileInput = ref(null)
const isProcessing = ref(false)

// 计算属性
const hasValidImages = computed(() => {
  return uploadedImages.value.some(image => image.base64)
})

/**
 * 处理拖拽进入
 */
const handleDragEnter = (event) => {
  if (props.disabled) return
  isDragOver.value = true
}

/**
 * 处理拖拽悬停
 */
const handleDragOver = (event) => {
  if (props.disabled) return
  isDragOver.value = true
}

/**
 * 处理拖拽离开
 */
const handleDragLeave = (event) => {
  // 检查是否真的离开了拖拽区域
  if (!event.currentTarget.contains(event.relatedTarget)) {
    isDragOver.value = false
  }
}

/**
 * 处理图片拖拽放置
 */
const handleDrop = async (event) => {
  if (props.disabled) return
  
  isDragOver.value = false
  const files = Array.from(event.dataTransfer.files)
  
  if (files.length === 0) return
  
  await processImages(files)
}

/**
 * 触发文件选择
 */
const triggerFileSelect = () => {
  if (props.disabled) return
  fileInput.value?.click()
}

/**
 * 处理文件选择
 */
const handleFileSelect = async (event) => {
  const files = Array.from(event.target.files)
  if (files.length === 0) return
  
  await processImages(files)
  
  // 清空文件输入，允许重复选择同一文件
  event.target.value = ''
}

/**
 * 处理图片列表
 */
const processImages = async (files) => {
  // 检查图片数量限制
  if (uploadedImages.value.length + files.length > props.maxImages) {
    emit('error', `最多只能选择 ${props.maxImages} 张图片`)
    return
  }

  // 过滤和验证图片
  const validImages = []
  for (const file of files) {
    // 检查文件大小
    if (file.size > props.maxFileSize) {
      emit('error', `图片 "${file.name}" 超过大小限制 (${formatFileSize(props.maxFileSize)})`)
      continue
    }

    // 检查文件类型
    if (!isValidImageType(file)) {
      emit('error', `不支持的图片格式: "${file.name}"，仅支持PNG和JPG格式`)
      continue
    }

    // 检查是否已存在
    if (uploadedImages.value.some(img => img.fileName === file.name && img.fileSize === file.size)) {
      emit('error', `图片 "${file.name}" 已存在`)
      continue
    }

    validImages.push(file)
  }

  if (validImages.length === 0) return

  // 开始处理图片
  isProcessing.value = true

  try {
    for (const file of validImages) {
      // 添加到列表（处理中状态）
      const imageItem = {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        base64: '',
        preview: '',
        processing: true,
        error: null
      }
      
      uploadedImages.value.push(imageItem)
      
      // 异步处理图片
      try {
        const base64 = await convertToBase64(file)
        const preview = await createPreview(file)
        
        // 更新图片状态
        const index = uploadedImages.value.findIndex(img => 
          img.fileName === file.name && img.fileSize === file.size && img.processing
        )
        
        if (index !== -1) {
          uploadedImages.value[index] = {
            ...imageItem,
            base64: base64,
            preview: preview,
            processing: false
          }
        }
      } catch (error) {
        // 更新错误状态
        const index = uploadedImages.value.findIndex(img => 
          img.fileName === file.name && img.fileSize === file.size && img.processing
        )
        
        if (index !== -1) {
          uploadedImages.value[index] = {
            ...imageItem,
            processing: false,
            error: error.message
          }
        }
      }
    }

    // 触发事件
    emitImagesChanged()
    
  } finally {
    isProcessing.value = false
  }
}

/**
 * 检查是否为有效的图片类型
 */
const isValidImageType = (file) => {
  const validTypes = ['image/png', 'image/jpeg', 'image/jpg']
  const validExtensions = ['.png', '.jpg', '.jpeg']
  
  return validTypes.includes(file.type) || 
         validExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
}

/**
 * 转换图片为Base64
 */
const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = () => {
      reject(new Error('图片转换失败'))
    }
    reader.readAsDataURL(file)
  })
}

/**
 * 创建图片预览
 */
const createPreview = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      resolve(reader.result)
    }
    reader.onerror = () => {
      reject(new Error('预览生成失败'))
    }
    reader.readAsDataURL(file)
  })
}

/**
 * 移除图片
 */
const removeImage = (index) => {
  uploadedImages.value.splice(index, 1)
  emitImagesChanged()
}

/**
 * 清空所有图片
 */
const clearAllImages = () => {
  uploadedImages.value = []
  emitImagesChanged()
}

/**
 * 触发图片变化事件
 */
const emitImagesChanged = () => {
  const validImages = uploadedImages.value.filter(image => image.base64)
  emit('images-changed', uploadedImages.value)
  emit('images-processed', validImages)
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法给父组件
defineExpose({
  clearAllImages,
  getProcessedImages: () => uploadedImages.value.filter(image => image.base64),
  hasValidImages
})
</script>

<style scoped>
.image-drag-drop-zone {
  position: relative;
  width: 100%;
}

.drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f9fafb;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover:not(.disabled) {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #dbeafe;
  transform: scale(1.02);
}

.drop-zone.has-images {
  text-align: left;
  align-items: stretch;
  min-height: auto;
  padding: 16px;
}

.drop-zone.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
}

.drop-icon {
  font-size: 16px;
  opacity: 0.6;
}

.drop-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.primary-text {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.secondary-text {
  font-size: 10px;
  color: #6b7280;
  margin: 0;
}

.hint-text {
  font-size: 9px;
  color: #9ca3af;
  margin: 0;
}

/* 图片列表样式 */
.images-list {
  width: 100%;
}

.images-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.images-count {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.clear-all-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s;
}

.clear-all-btn:hover {
  background-color: #fee2e2;
}

.images-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.image-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #ffffff;
  transition: all 0.2s;
}

.image-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.image-item.processing {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.image-item.error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

.image-preview {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  font-size: 20px;
  color: #9ca3af;
}

.image-info {
  flex: 1;
  min-width: 0;
}

.image-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.image-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
}

.image-size {
  color: #6b7280;
}

.processing-status {
  color: #3b82f6;
  font-weight: 500;
}

.error-status {
  color: #ef4444;
  font-weight: 500;
}

.success-status {
  color: #10b981;
  font-weight: 500;
}

.remove-image-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.remove-image-btn:hover {
  color: #ef4444;
  background-color: #fee2e2;
}

.add-more-section {
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.add-more-btn {
  background: none;
  border: 1px dashed #d1d5db;
  color: #6b7280;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s;
  width: 100%;
}

.add-more-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
  background-color: #eff6ff;
}

.add-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 处理中遮罩 */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-content p {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .drop-zone {
    border-color: #4b5563;
    background-color: #1f2937;
  }

  .drop-zone:hover:not(.disabled) {
    border-color: #60a5fa;
    background-color: #1e3a8a;
  }

  .drop-zone.drag-over {
    border-color: #60a5fa;
    background-color: #1e40af;
  }

  .image-item {
    background-color: #374151;
    border-color: #4b5563;
  }

  .image-name {
    color: #f3f4f6;
  }

  .images-count {
    color: #f3f4f6;
  }

  .primary-text {
    color: #f3f4f6;
  }

  .secondary-text {
    color: #d1d5db;
  }

  .hint-text {
    color: #9ca3af;
  }
}
</style>
