<template>
  <div class="file-attachment">
    <!-- 单个文件显示 -->
    <div v-if="!multiple && file" class="single-file">
      <div class="file-item" :class="{ 'error': file.error }">
        <div class="file-icon">
          {{ getFileIcon(file.fileName) }}
        </div>
        <div class="file-info">
          <div class="file-name" :title="file.fileName">
            {{ file.fileName }}
          </div>
          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
            <span v-if="file.error" class="error-status">解析失败</span>
            <span v-else-if="file.success" class="success-status">
              {{ file.content.length }} 字符
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 多个文件显示 -->
    <div v-else-if="multiple && files && files.length > 0" class="multiple-files">
      <div class="files-header">
        <span class="files-count">
          📎 {{ files.length }} 个附件
        </span>
        <button 
          v-if="showToggle && files.length > 1"
          class="toggle-btn"
          @click="toggleExpanded"
        >
          {{ isExpanded ? '收起' : '展开' }}
        </button>
      </div>

      <!-- 文件列表 -->
      <div class="files-list" :class="{ 'collapsed': !isExpanded && files.length > 1 }">
        <div 
          v-for="(file, index) in displayFiles" 
          :key="index"
          class="file-item"
          :class="{ 'error': file.error }"
        >
          <div class="file-icon">
            {{ getFileIcon(file.fileName) }}
          </div>
          <div class="file-info">
            <div class="file-name" :title="file.fileName">
              {{ file.fileName }}
            </div>
            <div class="file-meta">
              <span class="file-size">{{ formatFileSize(file.fileSize) }}</span>
              <span v-if="file.error" class="error-status">解析失败</span>
              <span v-else-if="file.success" class="success-status">
                {{ file.content.length }} 字符
              </span>
            </div>
          </div>
        </div>

        <!-- 折叠状态下的更多文件提示 -->
        <div v-if="!isExpanded && files.length > maxDisplayFiles" class="more-files">
          还有 {{ files.length - maxDisplayFiles }} 个文件...
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <span class="empty-text">无附件</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 组件属性
const props = defineProps({
  // 单个文件对象
  file: {
    type: Object,
    default: null
  },
  // 多个文件数组
  files: {
    type: Array,
    default: () => []
  },
  // 是否为多文件模式
  multiple: {
    type: Boolean,
    default: false
  },
  // 是否显示展开/收起按钮
  showToggle: {
    type: Boolean,
    default: true
  },
  // 折叠状态下最大显示文件数
  maxDisplayFiles: {
    type: Number,
    default: 2
  },
  // 是否默认展开
  defaultExpanded: {
    type: Boolean,
    default: false
  }
})

// 响应式数据
const isExpanded = ref(props.defaultExpanded)

// 计算属性
const displayFiles = computed(() => {
  if (!props.files || props.files.length === 0) return []
  
  if (props.multiple && !isExpanded.value && props.files.length > props.maxDisplayFiles) {
    return props.files.slice(0, props.maxDisplayFiles)
  }
  
  return props.files
})

/**
 * 切换展开状态
 */
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

/**
 * 获取文件图标
 */
const getFileIcon = (fileName) => {
  if (!fileName) return '📄'
  
  const ext = fileName.toLowerCase().split('.').pop()
  const iconMap = {
    'docx': '📄',
    'doc': '📄',
    'pdf': '📕',
    'txt': '📝',
    'xlsx': '📊',
    'xls': '📊',
    'pptx': '📊',
    'ppt': '📊'
  }
  return iconMap[ext] || '📄'
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-attachment {
  margin: 8px 0;
}

/* 单文件样式 */
.single-file .file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 13px;
}

/* 多文件样式 */
.multiple-files {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
}

.files-count {
  font-weight: 500;
  color: #475569;
}

.toggle-btn {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background-color: #dbeafe;
}

.files-list {
  max-height: 200px;
  overflow-y: auto;
}

.files-list.collapsed {
  max-height: none;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 13px;
  transition: background-color 0.2s;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: #f8fafc;
}

.file-item.error {
  background-color: #fef2f2;
}

.file-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.file-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 11px;
}

.file-size {
  color: #6b7280;
}

.error-status {
  color: #ef4444;
  font-weight: 500;
}

.success-status {
  color: #10b981;
  font-weight: 500;
}

.more-files {
  padding: 8px 12px;
  font-size: 11px;
  color: #6b7280;
  font-style: italic;
  text-align: center;
  background-color: #f8fafc;
}

/* 空状态 */
.empty-state {
  padding: 4px 0;
}

.empty-text {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .single-file .file-item,
  .multiple-files {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .files-header {
    background-color: #4b5563;
    border-color: #6b7280;
  }
  
  .files-count {
    color: #f3f4f6;
  }
  
  .file-name {
    color: #f3f4f6;
  }
  
  .file-item:hover {
    background-color: #4b5563;
  }
  
  .file-item.error {
    background-color: #7f1d1d;
  }
  
  .more-files {
    background-color: #4b5563;
  }
  
  .empty-text {
    color: #6b7280;
  }
}

/* 滚动条样式 */
.files-list::-webkit-scrollbar {
  width: 4px;
}

.files-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.files-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.files-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
