/**
 * 引用文件链接功能测试
 * 用于测试引用文件点击获取链接的功能
 */

/**
 * 模拟引用数据结构
 */
const mockCitationData = {
  fileName: "测试文档.pdf",
  sourceId: "source123",
  quotes: [
    {
      id: "quote1",
      question: "这是一个测试问题？",
      answer: "这是一个测试答案。",
      chunkIndex: 0,
      tokens: 50,
      score: 0.95,
      collectionId: "685ba2186130313d1d0809c5" // 测试用的 collectionId
    },
    {
      id: "quote2", 
      question: "另一个测试问题？",
      answer: "另一个测试答案。",
      chunkIndex: 1,
      tokens: 45,
      score: 0.88,
      collectionId: "685ba2186130313d1d0809c5" // 同一个文档的不同片段
    }
  ]
}

/**
 * 模拟用户数据
 */
const mockUserData = {
  username: "苏二川",
  employeeId: "012652"
}

/**
 * 测试获取文件URL的API调用
 */
async function testGetFileUrl() {
  console.log('=== 测试获取文件URL功能 ===')
  
  const collectionId = mockCitationData.quotes[0].collectionId
  const params = new URLSearchParams({
    collectionId: collectionId,
    username: mockUserData.username,
    jobnumber: mockUserData.employeeId
  })

  const testUrl = `http://192.168.1.225:4001/getFileUrlByCollectionId?${params}`
  console.log('请求URL:', testUrl)
  console.log('请求参数:', {
    collectionId: collectionId,
    username: mockUserData.username,
    jobnumber: mockUserData.employeeId
  })

  try {
    // 注意：这里只是模拟测试，实际测试需要真实的后台服务
    console.log('模拟API调用成功')
    console.log('预期返回: 文件URL字符串')
    return 'https://example.com/files/test-document.pdf'
  } catch (error) {
    console.error('API调用失败:', error)
    return null
  }
}

/**
 * 测试引用数据结构
 */
function testCitationDataStructure() {
  console.log('=== 测试引用数据结构 ===')
  
  // 检查引用数据是否包含必要字段
  console.log('引用文件名:', mockCitationData.fileName)
  console.log('引用片段数量:', mockCitationData.quotes.length)
  
  // 检查每个引用片段是否包含 collectionId
  mockCitationData.quotes.forEach((quote, index) => {
    console.log(`引用片段 ${index + 1}:`)
    console.log('  - ID:', quote.id)
    console.log('  - 问题:', quote.question)
    console.log('  - 答案:', quote.answer)
    console.log('  - CollectionId:', quote.collectionId)
    console.log('  - 是否有CollectionId:', !!quote.collectionId)
  })
}

/**
 * 测试查找包含 collectionId 的引用片段
 */
function testFindQuoteWithCollectionId() {
  console.log('=== 测试查找CollectionId ===')
  
  const quoteWithCollectionId = mockCitationData.quotes.find(quote => quote.collectionId)
  
  if (quoteWithCollectionId) {
    console.log('找到包含CollectionId的引用片段:')
    console.log('  - CollectionId:', quoteWithCollectionId.collectionId)
    console.log('  - 问题:', quoteWithCollectionId.question)
  } else {
    console.log('未找到包含CollectionId的引用片段')
  }
  
  return quoteWithCollectionId
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始运行引用文件链接功能测试...\n')
  
  // 测试数据结构
  testCitationDataStructure()
  console.log('\n')
  
  // 测试查找CollectionId
  const quoteWithCollectionId = testFindQuoteWithCollectionId()
  console.log('\n')
  
  // 测试API调用
  if (quoteWithCollectionId) {
    const fileUrl = await testGetFileUrl()
    console.log('获取到的文件URL:', fileUrl)
  }
  
  console.log('\n测试完成！')
}

// 导出测试函数
export {
  testGetFileUrl,
  testCitationDataStructure,
  testFindQuoteWithCollectionId,
  runAllTests,
  mockCitationData,
  mockUserData
}

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.citationLinkTest = {
    runAllTests,
    testGetFileUrl,
    testCitationDataStructure,
    testFindQuoteWithCollectionId,
    mockCitationData,
    mockUserData
  }
  
  console.log('引用文件链接测试已加载，可以通过 window.citationLinkTest.runAllTests() 运行测试')
}
