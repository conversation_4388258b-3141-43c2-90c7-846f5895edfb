<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引用文件显示功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-title {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-output {
            background: #1f2937;
            color: #d1d5db;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .status-success {
            color: #10b981;
            font-weight: 600;
        }
        
        .status-error {
            color: #ef4444;
            font-weight: 600;
        }
        
        .citation-preview {
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .citation-file {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }
        
        .citation-quote {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            border-left: 3px solid #3b82f6;
        }
        
        .quote-question {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .quote-answer {
            color: #6b7280;
        }
        
        .quote-meta {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 5px;
        }
        
        .problem-description {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .problem-description h4 {
            margin: 0 0 10px 0;
            color: #dc2626;
        }
        
        .solution-description {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .solution-description h4 {
            margin: 0 0 10px 0;
            color: #16a34a;
        }
    </style>
</head>
<body>
    <h1>引用文件显示功能测试</h1>
    
    <div class="problem-description">
        <h4>问题描述</h4>
        <p>修改引用文件点击逻辑后，引用文件不再显示。这是因为系统在模拟模式下运行，而模拟模式原本返回空的引用数组。</p>
    </div>
    
    <div class="solution-description">
        <h4>解决方案</h4>
        <p>修改了 <code>generateMockStreamResponse</code> 方法，添加了 <code>generateMockCitations</code> 方法来生成模拟引用数据，确保在模拟模式下也能显示引用文件。</p>
    </div>
    
    <div class="test-section">
        <div class="test-title">1. 模拟引用数据生成测试</div>
        <p>测试各个模块的模拟引用数据生成功能。</p>
        <button class="test-button" onclick="testMockCitationsGeneration()">测试引用数据生成</button>
        <div id="mockCitationsOutput" class="test-output" style="display: none;"></div>
        <div id="mockCitationsPreview"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. 引用数据结构验证</div>
        <p>验证生成的引用数据是否包含必要的字段，特别是 collectionId。</p>
        <button class="test-button" onclick="validateCitationStructure()">验证数据结构</button>
        <div id="structureValidationOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 流式回复引用测试</div>
        <p>测试流式回复方法是否正确返回引用数据。</p>
        <button class="test-button" onclick="testStreamResponseWithCitations()">测试流式回复</button>
        <div id="streamResponseOutput" class="test-output" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">4. 完整功能测试</div>
        <p>运行完整的功能测试，验证所有修复是否有效。</p>
        <button class="test-button" onclick="runCompleteTest()">运行完整测试</button>
        <div id="completeTestOutput" class="test-output" style="display: none;"></div>
    </div>

    <script>
        // 模拟 AI 配置
        const mockAIConfigs = {
            knowledge: {
                module: 'knowledge',
                enableMockMode: true,
                baseURL: '/api/v1',
                apiKey: 'test-key',
                model: 'test-model'
            },
            business: {
                module: 'business',
                enableMockMode: true,
                baseURL: '/api/v1',
                apiKey: 'test-key',
                model: 'test-model'
            },
            function: {
                module: 'function',
                enableMockMode: true,
                baseURL: '/api/v1',
                apiKey: 'test-key',
                model: 'test-model'
            }
        };

        // 模拟 AI 服务类
        class MockAIService {
            constructor(config) {
                this.config = config;
                this.module = config.module;
            }

            generateMockCitations() {
                const mockCitationsData = {
                    knowledge: [
                        {
                            fileName: "知识库文档.pdf",
                            sourceId: "mock_source_001",
                            quotes: [
                                {
                                    id: "mock_quote_001",
                                    question: "这是一个模拟的知识库问题示例？",
                                    answer: "这是一个模拟的知识库答案示例，用于演示引用功能。",
                                    chunkIndex: 0,
                                    tokens: 45,
                                    score: 0.95,
                                    collectionId: "685ba2186130313d1d0809c5"
                                },
                                {
                                    id: "mock_quote_002",
                                    question: "另一个模拟问题？",
                                    answer: "另一个模拟答案，展示多个引用片段的情况。",
                                    chunkIndex: 1,
                                    tokens: 38,
                                    score: 0.88,
                                    collectionId: "685ba2186130313d1d0809c5"
                                }
                            ]
                        },
                        {
                            fileName: "技术文档.docx",
                            sourceId: "mock_source_002",
                            quotes: [
                                {
                                    id: "mock_quote_003",
                                    question: "技术相关的模拟问题？",
                                    answer: "技术相关的模拟答案，用于测试引用文件链接功能。",
                                    chunkIndex: 0,
                                    tokens: 52,
                                    score: 0.92,
                                    collectionId: "685ba2186130313d1d0809c6"
                                }
                            ]
                        }
                    ],
                    business: [
                        {
                            fileName: "业务分析报告.xlsx",
                            sourceId: "mock_business_001",
                            quotes: [
                                {
                                    id: "mock_business_quote_001",
                                    question: "业务相关的模拟问题？",
                                    answer: "业务相关的模拟答案，展示业务域的引用功能。",
                                    chunkIndex: 0,
                                    tokens: 48,
                                    score: 0.90,
                                    collectionId: "685ba2186130313d1d0809c7"
                                }
                            ]
                        }
                    ],
                    function: [
                        {
                            fileName: "功能说明文档.md",
                            sourceId: "mock_function_001",
                            quotes: [
                                {
                                    id: "mock_function_quote_001",
                                    question: "功能相关的模拟问题？",
                                    answer: "功能相关的模拟答案，展示职能域的引用功能。",
                                    chunkIndex: 0,
                                    tokens: 42,
                                    score: 0.87,
                                    collectionId: "685ba2186130313d1d0809c8"
                                }
                            ]
                        }
                    ]
                };

                return mockCitationsData[this.module] || mockCitationsData.knowledge;
            }

            async generateMockStreamResponse() {
                const citations = this.generateMockCitations();
                return {
                    content: `这是一个模拟的${this.module}回复，用于测试引用功能。`,
                    citations: citations
                };
            }
        }

        // 测试函数
        window.testMockCitationsGeneration = function() {
            const output = document.getElementById('mockCitationsOutput');
            const preview = document.getElementById('mockCitationsPreview');
            output.style.display = 'block';
            
            let result = '=== 模拟引用数据生成测试 ===\n\n';
            let hasError = false;
            
            try {
                // 测试各个模块
                ['knowledge', 'business', 'function'].forEach(module => {
                    const service = new MockAIService(mockAIConfigs[module]);
                    const citations = service.generateMockCitations();
                    
                    result += `${module.toUpperCase()} 模块:\n`;
                    result += `  引用文件数量: ${citations.length}\n`;
                    
                    citations.forEach((citation, index) => {
                        result += `  文件 ${index + 1}: ${citation.fileName}\n`;
                        result += `    引用片段数量: ${citation.quotes.length}\n`;
                        citation.quotes.forEach((quote, qIndex) => {
                            result += `    片段 ${qIndex + 1}: ${quote.collectionId ? '✓' : '✗'} CollectionId\n`;
                        });
                    });
                    result += '\n';
                });
                
                result += '✓ 所有模块的引用数据生成成功\n';
                
                // 生成预览
                const knowledgeService = new MockAIService(mockAIConfigs.knowledge);
                const knowledgeCitations = knowledgeService.generateMockCitations();
                
                preview.innerHTML = '';
                knowledgeCitations.forEach(citation => {
                    const citationDiv = document.createElement('div');
                    citationDiv.className = 'citation-preview';
                    
                    let citationHTML = `<div class="citation-file">📄 ${citation.fileName}</div>`;
                    citation.quotes.forEach((quote, index) => {
                        citationHTML += `
                            <div class="citation-quote">
                                <div class="quote-question">Q: ${quote.question}</div>
                                <div class="quote-answer">A: ${quote.answer}</div>
                                <div class="quote-meta">
                                    CollectionId: ${quote.collectionId} | Tokens: ${quote.tokens} | Score: ${quote.score}
                                </div>
                            </div>
                        `;
                    });
                    
                    citationDiv.innerHTML = citationHTML;
                    preview.appendChild(citationDiv);
                });
                
            } catch (error) {
                result += `✗ 测试失败: ${error.message}\n`;
                hasError = true;
            }
            
            output.textContent = result;
            output.className = `test-output ${hasError ? 'status-error' : 'status-success'}`;
        };

        window.validateCitationStructure = function() {
            const output = document.getElementById('structureValidationOutput');
            output.style.display = 'block';
            
            let result = '=== 引用数据结构验证 ===\n\n';
            let hasError = false;
            
            try {
                const service = new MockAIService(mockAIConfigs.knowledge);
                const citations = service.generateMockCitations();
                
                result += '验证必要字段:\n';
                
                citations.forEach((citation, cIndex) => {
                    result += `文件 ${cIndex + 1}: ${citation.fileName}\n`;
                    result += `  ✓ fileName: ${citation.fileName}\n`;
                    result += `  ✓ sourceId: ${citation.sourceId}\n`;
                    result += `  ✓ quotes: ${citation.quotes.length} 个\n`;
                    
                    citation.quotes.forEach((quote, qIndex) => {
                        result += `  引用片段 ${qIndex + 1}:\n`;
                        result += `    ${quote.id ? '✓' : '✗'} id: ${quote.id}\n`;
                        result += `    ${quote.question ? '✓' : '✗'} question: ${quote.question ? '存在' : '缺失'}\n`;
                        result += `    ${quote.answer ? '✓' : '✗'} answer: ${quote.answer ? '存在' : '缺失'}\n`;
                        result += `    ${quote.collectionId ? '✓' : '✗'} collectionId: ${quote.collectionId || '缺失'}\n`;
                        result += `    ${quote.tokens ? '✓' : '✗'} tokens: ${quote.tokens || '缺失'}\n`;
                        result += `    ${quote.score ? '✓' : '✗'} score: ${quote.score || '缺失'}\n`;
                        
                        if (!quote.collectionId) {
                            hasError = true;
                        }
                    });
                });
                
                if (!hasError) {
                    result += '\n✓ 所有引用数据结构验证通过\n';
                } else {
                    result += '\n✗ 部分引用数据缺少必要字段\n';
                }
                
            } catch (error) {
                result += `✗ 验证失败: ${error.message}\n`;
                hasError = true;
            }
            
            output.textContent = result;
            output.className = `test-output ${hasError ? 'status-error' : 'status-success'}`;
        };

        window.testStreamResponseWithCitations = async function() {
            const output = document.getElementById('streamResponseOutput');
            output.style.display = 'block';
            
            let result = '=== 流式回复引用测试 ===\n\n';
            let hasError = false;
            
            try {
                const service = new MockAIService(mockAIConfigs.knowledge);
                
                result += '模拟流式回复调用...\n';
                const response = await service.generateMockStreamResponse();
                
                result += `回复类型: ${typeof response}\n`;
                result += `回复结构: ${Object.keys(response).join(', ')}\n`;
                result += `内容长度: ${response.content ? response.content.length : 0}\n`;
                result += `引用数量: ${response.citations ? response.citations.length : 0}\n\n`;
                
                if (response.citations && response.citations.length > 0) {
                    result += '引用详情:\n';
                    response.citations.forEach((citation, index) => {
                        result += `  ${index + 1}. ${citation.fileName} (${citation.quotes.length} 个片段)\n`;
                    });
                    result += '\n✓ 流式回复包含引用数据\n';
                } else {
                    result += '✗ 流式回复不包含引用数据\n';
                    hasError = true;
                }
                
            } catch (error) {
                result += `✗ 测试失败: ${error.message}\n`;
                hasError = true;
            }
            
            output.textContent = result;
            output.className = `test-output ${hasError ? 'status-error' : 'status-success'}`;
        };

        window.runCompleteTest = async function() {
            const output = document.getElementById('completeTestOutput');
            output.style.display = 'block';
            
            let result = '=== 完整功能测试 ===\n\n';
            let totalTests = 0;
            let passedTests = 0;
            
            try {
                // 测试1: 引用数据生成
                result += '测试1: 引用数据生成\n';
                totalTests++;
                try {
                    const service = new MockAIService(mockAIConfigs.knowledge);
                    const citations = service.generateMockCitations();
                    if (citations && citations.length > 0) {
                        result += '  ✓ 通过\n';
                        passedTests++;
                    } else {
                        result += '  ✗ 失败: 未生成引用数据\n';
                    }
                } catch (error) {
                    result += `  ✗ 失败: ${error.message}\n`;
                }
                
                // 测试2: 数据结构验证
                result += '测试2: 数据结构验证\n';
                totalTests++;
                try {
                    const service = new MockAIService(mockAIConfigs.knowledge);
                    const citations = service.generateMockCitations();
                    let structureValid = true;
                    
                    citations.forEach(citation => {
                        if (!citation.fileName || !citation.quotes || citation.quotes.length === 0) {
                            structureValid = false;
                        }
                        citation.quotes.forEach(quote => {
                            if (!quote.collectionId || !quote.id || !quote.question || !quote.answer) {
                                structureValid = false;
                            }
                        });
                    });
                    
                    if (structureValid) {
                        result += '  ✓ 通过\n';
                        passedTests++;
                    } else {
                        result += '  ✗ 失败: 数据结构不完整\n';
                    }
                } catch (error) {
                    result += `  ✗ 失败: ${error.message}\n`;
                }
                
                // 测试3: 流式回复
                result += '测试3: 流式回复\n';
                totalTests++;
                try {
                    const service = new MockAIService(mockAIConfigs.knowledge);
                    const response = await service.generateMockStreamResponse();
                    
                    if (response && response.content && response.citations && response.citations.length > 0) {
                        result += '  ✓ 通过\n';
                        passedTests++;
                    } else {
                        result += '  ✗ 失败: 流式回复格式不正确\n';
                    }
                } catch (error) {
                    result += `  ✗ 失败: ${error.message}\n`;
                }
                
                result += `\n测试总结: ${passedTests}/${totalTests} 通过\n`;
                
                if (passedTests === totalTests) {
                    result += '🎉 所有测试通过！引用文件显示功能已修复。\n';
                    result += '\n修复内容:\n';
                    result += '1. 添加了 generateMockCitations() 方法\n';
                    result += '2. 修改了 generateMockStreamResponse() 方法返回引用数据\n';
                    result += '3. 确保模拟模式下也能显示引用文件\n';
                    result += '4. 保持了引用数据的完整结构，包含 collectionId\n';
                } else {
                    result += '❌ 部分测试失败，需要进一步检查。\n';
                }
                
            } catch (error) {
                result += `✗ 测试过程中发生错误: ${error.message}\n`;
            }
            
            output.textContent = result;
            output.className = `test-output ${passedTests === totalTests ? 'status-success' : 'status-error'}`;
        };
    </script>
</body>
</html>
