<template>
  <div class="ai-message-container">
    <!-- AI头像 -->
    <div class="ai-avatar">
      <img src="../../../public/favicon.ico" alt="AI头像" />
    </div>
    
    <!-- 消息内容区域 -->
    <div class="ai-content-wrapper">
      <!-- 推理过程（可折叠） - 已禁用 -->
      <div v-if="shouldShowReasoning && (hasReasoning || message.reasoningActive)" class="reasoning-section">
        <!-- 调试信息 -->
        <div v-if="false" style="font-size: 12px; color: #999; margin-bottom: 8px;">
          调试: hasReasoning={{ hasReasoning }}, reasoningActive={{ message.reasoningActive }},
          reasoningSteps={{ message.reasoningSteps?.length || 0 }},
          enableReasoning={{ aiConfig.enableReasoning }}
        </div>
        <button
          class="reasoning-toggle"
          @click="toggleReasoning"
          :class="{ 'expanded': showReasoning }"
        >
          <svg class="toggle-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>
          <span class="toggle-text">
            {{ message.reasoningActive ? '正在思考...' : '推理过程' }}
          </span>
          <span v-if="!message.reasoningActive" class="reasoning-badge">{{ reasoningSteps.length }}</span>
          <div v-else class="thinking-indicator">
            <div class="thinking-dots">
              <span></span><span></span><span></span>
            </div>
          </div>
        </button>

        <div v-show="showReasoning" class="reasoning-content">
          <!-- 流式推理过程内容 -->
          <div v-if="message.reasoningContent" class="thinking-content">
            <div class="thinking-header">
              <span class="thinking-icon">🧠</span>
              <span class="thinking-title">AI思维过程</span>
              <span v-if="message.reasoningActive" class="thinking-status">思考中...</span>
            </div>
            <div class="thinking-text">
              <MarkdownRenderer :content="message.reasoningContent" />
              <div v-if="message.reasoningActive" class="typing-cursor"></div>
            </div>
          </div>

          <!-- 真实推理过程文本（兼容旧格式） -->
          <div v-else-if="message.thinkingContent" class="thinking-content">
            <div class="thinking-header">
              <span class="thinking-icon">🧠</span>
              <span class="thinking-title">AI思维过程</span>
            </div>
            <div class="thinking-text">
              <MarkdownRenderer :content="message.thinkingContent" />
            </div>
          </div>

          <!-- 动态推理步骤（当没有真实推理内容时显示） -->
          <div v-else>
            <div
              v-for="(step, index) in dynamicReasoningSteps"
              :key="index"
              class="reasoning-step"
              :class="getStepClass(step, index)"
            >
              <div class="step-indicator">
                <div v-if="step.status === 'thinking'" class="step-thinking">
                  <div class="thinking-spinner"></div>
                </div>
                <div v-else-if="step.status === 'completed'" class="step-completed">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                  </svg>
                </div>
                <div v-else class="step-number">{{ index + 1 }}</div>
              </div>
              <div class="step-content">
                <div class="step-header">
                  <span class="step-icon">{{ step.icon }}</span>
                  <span class="step-title">{{ step.title }}</span>
                  <span v-if="step.status === 'thinking'" class="step-status">思考中...</span>
                  <span v-else-if="step.status === 'completed'" class="step-status completed">已完成</span>
                </div>
                <div class="step-description">
                  <MarkdownRenderer v-if="step.type === 'thinking_content'" :content="step.description" />
                  <span v-else>{{ step.description }}</span>
                </div>
                <div v-if="step.startTime && step.endTime" class="step-timing">
                  耗时: {{ Math.round((step.endTime - step.startTime) / 1000 * 100) / 100 }}秒
                </div>
              </div>
            </div>

            <!-- 推理进度条 -->
            <div v-if="message.reasoningActive" class="reasoning-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: reasoningProgress + '%' }"
                ></div>
              </div>
              <div class="progress-text">
                {{ Math.round(reasoningProgress) }}% 完成
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 主要回复内容 -->
      <div class="main-content">
        <div class="message-bubble" :class="messageClasses">
          <!-- 加载状态 -->
          <div v-if="message.loading" class="loading-container">
            <div class="loading-dots">
              <span></span><span></span><span></span>
            </div>
            <span class="loading-text">正在思考中...</span>
          </div>
          
          <!-- 流式输出内容 -->
          <div v-else-if="message.streaming" class="streaming-content">
            <MarkdownRenderer :content="safeStreamingContent" />
            <div class="typing-cursor"></div>
          </div>

          <!-- 完整内容 -->
          <div v-else class="final-content">
            <MarkdownRenderer :content="safeMessageContent" />
          </div>
          
          <!-- 错误状态 -->
          <div v-if="message.error" class="error-indicator">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span>回复出现错误</span>
          </div>
        </div>

        <!-- 引用文件部分 -->
        <div v-if="message.citations && message.citations.length > 0" class="citations-section">
          <div class="citations-header">
            <svg class="citations-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C3.89,3 19,3.89 19,3Z"/>
            </svg>
            <span class="citations-title">引用文件 ({{ message.citations.length }})</span>
          </div>
          <div class="citations-list">
            <div
              v-for="citation in message.citations"
              :key="citation.fileName"
              class="citation-item"
              @click="showCitationDetail(citation)"
            >
              <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
              <span class="file-name">{{ citation.fileName }}</span>
              <span class="quote-count">({{ citation.quotes.length }})</span>
            </div>
          </div>
        </div>

        <!-- 消息时间和操作 -->
        <div class="message-footer">
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          <div class="message-actions">
            <button 
              v-if="!message.loading && !message.error" 
              class="action-btn copy-btn"
              @click="copyContent"
              title="复制内容"
            >
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              </svg>
            </button>
            <button 
              v-if="message.error" 
              class="action-btn retry-btn"
              @click="$emit('retry')"
              title="重试"
            >
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引用详情弹窗 -->
  <div v-if="showCitationModal" class="citation-modal-overlay" @click="closeCitationModal">
    <div class="citation-modal" @click.stop>
      <!-- 使用现有的水印组件包裹弹窗内容 -->
      <div class="citation-modal-header">
        <h3 class="citation-modal-title">
          <a
            v-if="selectedCitation?.fileUrl"
            :href="selectedCitation.fileUrl"
            target="_blank"
            class="file-link"
            @click="handleFileLinkClick"
          >
            {{ selectedCitation?.fileName }}
          </a>
          <span v-else class="file-name-loading" @click="handleFileNameClick">
            {{ selectedCitation?.fileName }}
            <span v-if="loadingFileUrl" class="loading-indicator">获取链接中...</span>
          </span>
        </h3>
        <button class="citation-modal-close" @click="closeCitationModal">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
          </svg>
        </button>
      </div>
      <div class="citation-modal-content">
        <div v-if="selectedCitation?.quotes && selectedCitation.quotes.length > 0" class="quotes-list">
          <div v-for="(quote, index) in selectedCitation.quotes" :key="quote.id" class="quote-item">
            <div class="quote-header">
              <span class="quote-index">引用片段 {{ index + 1 }}</span>
              <span v-if="quote.tokens" class="quote-tokens">{{ quote.tokens }} tokens</span>
            </div>
            <div v-if="quote.question" class="quote-question">
              <strong>问题：</strong>
              <div class="quote-content">
                <MarkdownRenderer :content="quote.question" />
              </div>
            </div>
            <div v-if="quote.answer" class="quote-answer">
              <strong>答案：</strong>
              <div class="quote-content">
                <MarkdownRenderer :content="quote.answer" />
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-quotes">
          <p>暂无引用片段</p>
        </div>
      </div>
      <!-- 使用现有的水印组件 -->
      <SimpleWatermark :module="message.module || 'knowledge'" class="citation-watermark" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import MarkdownRenderer from './MarkdownRenderer.vue'
import SimpleWatermark from './SimpleWatermark.vue'

// 组件属性
const props = defineProps({
  // 消息对象
  message: {
    type: Object,
    required: true
  },
  // 流式输出内容
  streamingContent: {
    type: String,
    default: ''
  }
})

// 组件事件
const emit = defineEmits(['retry'])

// 响应式数据
const showReasoning = ref(false)
const showCitationModal = ref(false)
const selectedCitation = ref(null)
const loadingFileUrl = ref(false)

// 获取AI配置
import { getAIConfig } from '../../config/ai.js'
const aiConfig = getAIConfig(props.message.module || 'knowledge')

// 导入用户服务
import { getUserService } from '../../services/userService.js'
const userService = getUserService()

// 监听推理过程状态变化，自动展开和折叠
watch(() => props.message.reasoningActive, (isActive, wasActive) => {
  if (isActive) {
    // 推理开始时自动展开
    showReasoning.value = true
  } else if (wasActive === true && isActive === false) {
    // 推理完成时延迟自动折叠
    setTimeout(() => {
      showReasoning.value = false
    }, 2000) // 2秒后自动折叠
  }
}, { immediate: true })

// 监听推理内容变化，确保推理完成后处于折叠状态
watch(() => props.message.reasoningContent, (content) => {
  if (content && !props.message.reasoningActive) {
    // 推理内容存在但推理未激活，说明推理已完成
    // 延迟自动折叠，确保用户看到推理过程已完成
    setTimeout(() => {
      showReasoning.value = false
    }, 2000) // 2秒后自动折叠
  }
}, { immediate: true })

// 计算属性
const messageClasses = computed(() => ({
  'loading-message': props.message.loading,
  'error-message': props.message.error,
  'streaming-message': props.message.streaming
}))

// 判断是否应该显示推理过程
const shouldShowReasoning = computed(() => {
  // 如果有推理内容（流式推理过程）或推理步骤，则显示推理过程
  return !!(props.message.reasoningContent ||
           (props.message.reasoningSteps && props.message.reasoningSteps.length > 0) ||
           (props.message.reasoning && props.message.reasoning.length > 0) ||
           props.message.thinkingContent)
})

// 推理步骤处理
const reasoningSteps = computed(() => {
  // 优先使用动态推理步骤，否则使用静态推理数据
  if (props.message.reasoningSteps && props.message.reasoningSteps.length > 0) {
    return props.message.reasoningSteps
  }

  // 兼容旧的静态推理数据
  if (props.message.reasoning && props.message.reasoning.length > 0) {
    return props.message.reasoning
  }

  // 如果推理过程正在进行中，返回空数组但仍然显示推理区域
  if (props.message.reasoningActive) {
    return []
  }

  return []
})

// 动态推理步骤（包含状态信息）
const dynamicReasoningSteps = computed(() => {
  if (props.message.reasoningSteps && props.message.reasoningSteps.length > 0) {
    return props.message.reasoningSteps
  }

  // 兼容旧的静态推理数据，转换为动态格式
  return reasoningSteps.value.map((step, index) => ({
    ...step,
    status: 'completed',
    icon: step.icon || '✓'
  }))
})

const hasReasoning = computed(() => {
  // 检查是否启用推理过程功能
  if (!aiConfig.enableReasoning) {
    return false
  }

  // 检查是否有真实的思维内容、推理内容或推理步骤
  const hasThinkingContent = props.message.thinkingContent && props.message.thinkingContent.trim().length > 0
  const hasReasoningContent = props.message.reasoningContent && props.message.reasoningContent.trim().length > 0
  const hasSteps = reasoningSteps.value.length > 0
  const isActive = props.message.reasoningActive

  const shouldShow = (hasThinkingContent || hasReasoningContent || hasSteps || isActive) && !props.message.loading

  return shouldShow
})

// 推理进度计算
const reasoningProgress = computed(() => {
  if (!props.message.reasoningSteps || props.message.reasoningSteps.length === 0) {
    return 0
  }

  const completedSteps = props.message.reasoningSteps.filter(step => step.status === 'completed').length
  const totalSteps = props.message.reasoningSteps.length

  return (completedSteps / totalSteps) * 100
})

// 安全的消息内容处理
const safeMessageContent = computed(() => {
  const content = props.message.content

  // 简单的类型检查和转换
  if (content === null || content === undefined) {
    return ''
  }

  if (typeof content === 'object') {
    console.error('AIMessage接收到对象类型的消息内容:', content)
    try {
      return JSON.stringify(content, null, 2)
    } catch (error) {
      return '内容格式错误'
    }
  }

  return String(content)
})

// 安全的流式内容处理
const safeStreamingContent = computed(() => {
  const content = props.streamingContent

  // 简单的类型检查和转换
  if (content === null || content === undefined) {
    return ''
  }

  if (typeof content === 'object') {
    console.error('AIMessage接收到对象类型的流式内容:', content)
    try {
      return JSON.stringify(content, null, 2)
    } catch (error) {
      return '内容格式错误'
    }
  }

  return String(content)
})

// 方法
const toggleReasoning = () => {
  showReasoning.value = !showReasoning.value
}

// 显示引用详情
const showCitationDetail = (citation) => {
  selectedCitation.value = citation
  showCitationModal.value = true

  // 在下一个tick中计算并设置弹窗位置
  nextTick(() => {
    setCitationModalPosition()
  })
}

// 设置引用弹窗位置，使其在会话区域居中
const setCitationModalPosition = () => {
  const overlay = document.querySelector('.citation-modal-overlay')
  if (!overlay) return

  // 查找会话区域容器
  const chatArea = document.querySelector('.chat-area') ||
                   document.querySelector('.chat-container') ||
                   document.querySelector('.main-content') ||
                   document.querySelector('.conversation-area')

  if (chatArea) {
    const chatRect = chatArea.getBoundingClientRect()

    // 设置弹窗覆盖层的位置和尺寸，限制在会话区域内
    overlay.style.top = chatRect.top + 'px'
    overlay.style.left = chatRect.left + 'px'
    overlay.style.width = chatRect.width + 'px'
    overlay.style.height = chatRect.height + 'px'
    overlay.style.right = 'auto'
    overlay.style.bottom = 'auto'
  } else {
    // 如果找不到会话区域，回退到全屏显示
    overlay.style.top = '0'
    overlay.style.left = '0'
    overlay.style.right = '0'
    overlay.style.bottom = '0'
    overlay.style.width = 'auto'
    overlay.style.height = 'auto'
  }
}

// 关闭引用详情弹窗
const closeCitationModal = () => {
  showCitationModal.value = false
  selectedCitation.value = null
  loadingFileUrl.value = false
}

// 获取文件URL
const getFileUrl = async (collectionId) => {
  try {
    const currentUser = await userService.getCurrentUser()
    if (!currentUser) {
      console.error('用户未登录，无法获取文件链接')
      return null
    }

    const params = new URLSearchParams({
      collectionId: collectionId,
      username: currentUser.username,
      jobnumber: currentUser.employeeId
    })

    const response = await fetch(`http://192.168.1.225:4001/getFileUrlByCollectionId?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const fileUrl = await response.text()
    return fileUrl.trim()
  } catch (error) {
    console.error('获取文件URL失败:', error)
    return null
  }
}

// 处理文件名点击事件
const handleFileNameClick = async () => {
  if (!selectedCitation.value || loadingFileUrl.value) {
    return
  }

  // 查找包含 collectionId 的引用片段
  const quoteWithCollectionId = selectedCitation.value.quotes?.find(quote => quote.collectionId)
  if (!quoteWithCollectionId || !quoteWithCollectionId.collectionId) {
    console.error('未找到 collectionId，无法获取文件链接')
    return
  }

  loadingFileUrl.value = true

  try {
    const fileUrl = await getFileUrl(quoteWithCollectionId.collectionId)
    if (fileUrl) {
      // 将URL保存到selectedCitation中，并打开链接
      selectedCitation.value.fileUrl = fileUrl
      window.open(fileUrl, '_blank')
    } else {
      console.error('获取文件链接失败')
    }
  } finally {
    loadingFileUrl.value = false
  }
}

// 处理文件链接点击事件
const handleFileLinkClick = (event) => {
  // 阻止默认行为，我们手动处理链接打开
  event.preventDefault()
  if (selectedCitation.value?.fileUrl) {
    window.open(selectedCitation.value.fileUrl, '_blank')
  }
}

// 监听窗口大小变化，重新计算弹窗位置
const handleResize = () => {
  if (showCitationModal.value) {
    setCitationModalPosition()
  }
}

// 监听弹窗显示状态变化
watch(showCitationModal, (newValue) => {
  if (newValue) {
    // 弹窗显示时添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)
  } else {
    // 弹窗关闭时移除监听器
    window.removeEventListener('resize', handleResize)
  }
})

const getStepClass = (step, index) => {
  const classes = []

  if (step.status === 'thinking') {
    classes.push('step-thinking-active')
  } else if (step.status === 'completed') {
    classes.push('step-completed-active')
  }

  if (props.message.currentReasoningStep === index) {
    classes.push('step-current')
  }

  return classes.join(' ')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const copyContent = async () => {
  try {
    // 使用安全处理后的内容
    const content = props.message.streaming ? safeStreamingContent.value : safeMessageContent.value

    if (!content || content.trim() === '') {
      console.warn('没有可复制的内容')
      return
    }

    await navigator.clipboard.writeText(content)
    console.log('内容已复制到剪贴板')
    // 这里可以添加复制成功的提示
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = content
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      console.log('使用降级方案复制成功')
    } catch (fallbackError) {
      console.error('降级复制方案也失败:', fallbackError)
    }
  }
}
</script>

<style scoped>
/* AI消息容器 */
.ai-message-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

/* AI头像 */
.ai-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--bg-tertiary, #f3f4f6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-avatar img {
  width: 70%;
  height: 70%;
  object-fit: cover;
}

/* 内容包装器 */
.ai-content-wrapper {
  flex: 1;
  min-width: 0;
}

/* 推理过程区域 */
.reasoning-section {
  margin-bottom: 12px;
}

.reasoning-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--bg-secondary, #f8fafc);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--text-secondary, #64748b);
  width: 100%;
  text-align: left;
}

.reasoning-toggle:hover {
  background-color: var(--bg-tertiary, #f1f5f9);
  border-color: var(--border-secondary, #cbd5e1);
}

.reasoning-toggle.expanded {
  background-color: var(--accent-secondary, #eff6ff);
  border-color: var(--accent-primary, #3b82f6);
  color: var(--accent-primary, #3b82f6);
}

.toggle-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.reasoning-toggle.expanded .toggle-icon {
  transform: rotate(180deg);
}

.reasoning-badge {
  background-color: #3b82f6;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.thinking-indicator {
  margin-left: auto;
}

.thinking-dots {
  display: flex;
  gap: 2px;
}

.thinking-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #3b82f6;
  animation: thinking-bounce 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.reasoning-content {
  margin-top: 8px;
  padding: 12px;
  background-color: var(--bg-secondary, #fafbfc);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 8px;
}

.reasoning-step {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.reasoning-step:last-child {
  margin-bottom: 0;
}

.reasoning-step.step-thinking-active {
  background-color: var(--info-bg, #eff6ff);
  border-radius: 8px;
  padding: 8px;
  margin: 4px 0;
}

.reasoning-step.step-completed-active {
  background-color: var(--success-bg, #f0fdf4);
  border-radius: 8px;
  padding: 8px;
  margin: 4px 0;
}

.reasoning-step.step-current {
  border-left: 3px solid var(--accent-primary, #3b82f6);
  padding-left: 9px;
}

.step-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-number {
  background-color: var(--accent-primary, #3b82f6);
  color: white;
}

.step-thinking {
  background-color: var(--warning-border, #fbbf24);
  color: white;
}

.step-completed {
  background-color: var(--success-border, #10b981);
  color: white;
}

.step-completed svg {
  width: 14px;
  height: 14px;
}

.thinking-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.step-content {
  flex: 1;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.step-icon {
  font-size: 16px;
}

.step-title {
  font-weight: 600;
  color: #374151;
  flex: 1;
}

.step-status {
  font-size: 12px;
  color: #6b7280;
  font-weight: normal;
}

.step-status.completed {
  color: #10b981;
}

.step-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.step-timing {
  font-size: 12px;
  color: #9ca3af;
  font-style: italic;
}

/* 推理进度条 */
.reasoning-progress {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

/* 真实思维过程样式 */
.thinking-content {
  background-color: var(--bg-secondary, #f8fafc);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-primary, #e2e8f0);
}

.thinking-icon {
  font-size: 18px;
}

.thinking-title {
  font-weight: 600;
  color: var(--text-primary, #374151);
  font-size: 14px;
  flex: 1;
}

.thinking-status {
  font-size: 12px;
  color: #3b82f6;
  font-weight: normal;
  animation: pulse 2s infinite;
}

.thinking-text {
  color: var(--text-secondary, #4b5563);
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.thinking-text :deep(p) {
  margin-bottom: 8px;
}

.thinking-text :deep(p:last-child) {
  margin-bottom: 0;
}

.thinking-text :deep(code) {
  background-color: var(--bg-tertiary, #f1f5f9);
  color: var(--text-primary, #374151);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 13px;
}

.thinking-text :deep(pre) {
  background-color: var(--bg-tertiary, #f1f5f9);
  color: var(--text-primary, #374151);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
}

.thinking-text :deep(blockquote) {
  border-left: 3px solid var(--border-secondary, #d1d5db);
  padding-left: 12px;
  margin: 8px 0;
  color: var(--text-tertiary, #6b7280);
  font-style: italic;
}

/* 主要内容区域 */
.main-content {
  width: 100%;
}

.message-bubble {
  background-color: var(--bg-secondary, #f8fafc);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  padding: 16px;
  position: relative;
  transition: all 0.2s ease;
}

.message-bubble.loading-message {
  background-color: var(--bg-secondary, #f8fafc);
}

.message-bubble.error-message {
  background-color: var(--error-bg, #fef2f2);
  border-color: var(--error-border, #ef4444);
}

.message-bubble.streaming-message {
  background-color: var(--info-bg, #f0f9ff);
  border-color: var(--info-border, #0ea5e9);
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #6b7280;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: loading-bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.loading-text {
  font-size: 14px;
}

/* 流式输出 */
.streaming-content {
  position: relative;
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #3b82f6;
  margin-left: 2px;
  animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 错误指示器 */
.error-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ef4444;
  font-size: 14px;
}

.error-indicator svg {
  width: 16px;
  height: 16px;
}

/* 消息底部 */
.message-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding-top: 8px;
}

.message-time {
  font-size: 12px;
  color: #9ca3af;
}

.message-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ai-message-container:hover .message-actions {
  opacity: 1;
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #6b7280;
}

.action-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.action-btn svg {
  width: 14px;
  height: 14px;
}

.copy-btn:hover {
  background-color: #dbeafe;
  color: #3b82f6;
}

.retry-btn:hover {
  background-color: #fef2f2;
  color: #ef4444;
}

/* 响应式设计 */

/* 平板端适配 */
@media (max-width: 1024px) {
  .ai-message-container {
    padding: var(--spacing-md);
  }

  .ai-avatar {
    width: 36px;
    height: 36px;
  }

  .avatar-icon {
    font-size: var(--font-size-lg);
  }

  .message-bubble {
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
  }

  .message-time {
    font-size: var(--font-size-xs);
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  .action-btn svg {
    width: 12px;
    height: 12px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .ai-message-container {
    padding: var(--spacing-sm);
  }

  .ai-avatar {
    width: 32px;
    height: 32px;
    margin-right: var(--spacing-sm);
  }

  .avatar-icon {
    font-size: var(--font-size-base);
  }

  .message-bubble {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
    line-height: 1.5;
  }

  .message-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .message-actions {
    opacity: 1; /* 移动端始终显示操作按钮 */
  }

  .action-btn {
    width: 32px;
    height: 32px;
    touch-action: manipulation; /* 优化触摸体验 */
  }

  .action-btn svg {
    width: 14px;
    height: 14px;
  }

  .loading-text {
    font-size: var(--font-size-sm);
  }

  .error-indicator {
    font-size: var(--font-size-sm);
  }
}

/* 小屏手机适配 */
@media (max-width: 480px) {
  .ai-message-container {
    padding: var(--spacing-xs);
  }

  .ai-avatar {
    width: 28px;
    height: 28px;
    margin-right: var(--spacing-xs);
  }

  .avatar-icon {
    font-size: var(--font-size-sm);
  }

  .message-bubble {
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  .message-time {
    font-size: 10px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  .action-btn svg {
    width: 12px;
    height: 12px;
  }
}

/* 引用文件样式 */
.citations-section {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary, #f8fafc);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color, #e2e8f0);
}

.citations-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-primary, #374151);
}

.citations-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-color, #3b82f6);
}

.citations-title {
  font-size: 14px;
}

.citations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.citation-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.citation-item:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-sm);
  transform: translateX(2px);
}

.file-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-color, #3b82f6);
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-size: 13px;
  color: var(--text-primary, #374151);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.quote-count {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-secondary, #f8fafc);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

/* 引用详情弹窗样式 */
.citation-modal-overlay {
  position: fixed;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
  /* 动态设置位置和尺寸，限制在会话区域内 */
}

.citation-modal {
  background: var(--bg-primary, #ffffff);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: min(800px, 90%);
  width: 100%;
  max-height: min(80vh, calc(100% - 40px));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: 20px;
  position: relative;
}

.citation-watermark {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.citation-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color, #e2e8f0);
  background: var(--bg-secondary, #f8fafc);
}

.citation-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #374151);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.citation-modal-title .file-link {
  color: var(--primary-color, #3b82f6);
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.citation-modal-title .file-link:hover {
  color: var(--primary-color-hover, #2563eb);
  text-decoration: underline;
}

.citation-modal-title .file-name-loading {
  cursor: pointer;
  color: var(--text-primary, #374151);
  transition: color 0.2s ease;
}

.citation-modal-title .file-name-loading:hover {
  color: var(--primary-color, #3b82f6);
}

.citation-modal-title .loading-indicator {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  margin-left: 8px;
  font-weight: normal;
}

.citation-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  color: var(--text-secondary, #6b7280);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.citation-modal-close:hover {
  background: var(--bg-hover, #f1f5f9);
  color: var(--text-primary, #374151);
}

.citation-modal-close svg {
  width: 20px;
  height: 20px;
}

.citation-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}



.quotes-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.quote-item {
  padding: var(--spacing-md);
  background: var(--bg-secondary, #f8fafc);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color, #e2e8f0);
}

.quote-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.quote-index {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color, #3b82f6);
}

.quote-tokens {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-primary, #ffffff);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.quote-question,
.quote-answer {
  margin-bottom: var(--spacing-sm);
}

.quote-question:last-child,
.quote-answer:last-child {
  margin-bottom: 0;
}

.quote-question strong,
.quote-answer strong {
  color: var(--text-primary, #374151);
  font-size: 14px;
}

.quote-content {
  margin: var(--spacing-xs) 0 0 0;
  color: var(--text-secondary, #6b7280);
  font-size: 13px;
  line-height: 1.5;
}

.quote-content :deep(p) {
  margin: 0.5em 0;
}

.quote-content :deep(pre) {
  background-color: var(--bg-secondary);
  padding: 0.5em;
  border-radius: var(--border-radius-sm);
  overflow-x: auto;
}

.quote-content :deep(code) {
  font-family: monospace;
  background-color: var(--bg-secondary);
  padding: 0.1em 0.3em;
  border-radius: var(--border-radius-sm);
  font-size: 0.9em;
}

.quote-content :deep(ul), .quote-content :deep(ol) {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.quote-content :deep(blockquote) {
  border-left: 3px solid var(--border-color);
  padding-left: 0.8em;
  margin: 0.5em 0;
  color: var(--text-secondary);
}

.no-quotes {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary, #6b7280);
}

.no-quotes p {
  margin: 0;
  font-size: 14px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .citation-modal-overlay {
    padding: var(--spacing-xs);
  }

  .citation-modal {
    max-width: min(95%, calc(100% - 20px));
    max-height: min(90vh, calc(100% - 20px));
    margin: 10px;
  }

  .citation-modal-header {
    padding: var(--spacing-md);
  }

  .citation-modal-title {
    font-size: 16px;
  }

  .citation-modal-content {
    padding: var(--spacing-md);
  }

  .quotes-list {
    gap: var(--spacing-md);
  }

  .quote-item {
    padding: var(--spacing-sm);
  }
}
</style>
